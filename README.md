# 积分游戏投注策略分析系统

## 项目简介

这是一个基于历史开奖数据的积分游戏投注策略分析系统，通过多种算法分析历史数据，生成最佳投注策略，帮助用户最大化积分收益。

## 功能特点

### 🔍 数据分析功能
- **频率分析**: 分析各数字在各位置的出现频率
- **概率计算**: 计算各数字在各位置的概率分布
- **热号冷号分析**: 识别当前热门和冷门数字
- **连续性分析**: 分析数字连续出现的模式
- **趋势分析**: 识别数字出现的趋势变化

### 📊 投注策略
- **高频数字策略**: 基于历史高频数字的投注策略
- **热号策略**: 基于当前热号的投注策略
- **均衡策略**: 基于概率均衡的投注策略
- **冷号反弹策略**: 基于冷号反弹理论的投注策略

### 📈 收益分析
- **历史收益率计算**: 计算各策略的历史收益率
- **胜率统计**: 统计各策略的胜率
- **风险评估**: 评估投注风险
- **最佳策略推荐**: 自动推荐收益率最高的策略

### 📋 报告生成
- **详细分析报告**: 生成完整的文字分析报告
- **可视化图表**: 生成多种统计图表
- **投注建议**: 提供具体的投注建议

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install -r requirements.txt
```

主要依赖包：
- `mysql-connector-python`: MySQL数据库连接
- `pandas`: 数据处理和分析
- `numpy`: 数值计算
- `matplotlib`: 图表绘制
- `seaborn`: 统计图表
- `scipy`: 科学计算

## 配置说明

### 数据库配置
编辑 `配置.ini` 文件，设置MySQL数据库连接信息：

```ini
[database]
host = localhost
user = root
password = root
database = 开奖结果
port = 3306
```

### 数据库表结构
系统需要访问 `sgwin_history` 表，表结构如下：
- `period`: 期数
- `drawTime`: 开奖时间
- `gameType`: 游戏类型 (需要为0)
- `b1` - `b10`: 开奖结果 (1-10的不重复数字)

## 使用方法

### 1. 快速开始
```bash
# 运行完整分析
python 积分游戏投注策略分析.py
```

### 2. 测试系统
```bash
# 运行测试脚本
python 测试脚本.py
```

### 3. 自定义分析
```python
from 积分游戏投注策略分析 import 积分游戏分析器

# 创建分析器
分析器 = 积分游戏分析器()

# 获取数据
分析器.获取历史数据()
分析器.数据预处理()

# 进行分析
分析器.频率分析()
分析器.概率计算()
分析器.热号冷号分析()

# 生成策略
分析器.生成投注策略()
策略评估 = 分析器.评估所有策略()

# 生成报告
报告 = 分析器.生成详细报告()
分析器.保存报告(报告)
```

## 输出文件

运行完成后，系统会生成以下文件：

1. **积分游戏投注策略分析报告.txt**: 详细的文字分析报告
2. **积分游戏分析图表.png**: 可视化统计图表

## 游戏规则

### 开奖规则
- 每次开奖产生10个球(b1-b10)
- 每个球从1-10的数字中随机选择一个不重复的数字
- 最终结果为1-10的完整排列

### 投注规则
- 可以投注任意位置的任意数字
- 每个数字投注1积分
- 中奖获得9.9倍积分收益

## 分析算法

### 1. 频率分析算法
- 统计各数字在各位置的历史出现次数
- 计算出现频率和概率分布
- 识别高频和低频数字

### 2. 热号冷号算法
- 基于最近N期数据计算期望频率
- 超过期望10%为热号，低于期望10%为冷号
- 动态调整热号冷号列表

### 3. 连续性分析算法
- 分析数字连续出现的模式
- 计算平均连续次数和最大连续次数
- 预测连续性趋势

### 4. 策略优化算法
- 基于历史数据回测各策略收益率
- 计算风险调整后的收益率
- 自动选择最优策略组合

## 风险提示

⚠️ **重要提示**：
1. 本系统基于历史数据分析，不保证未来收益
2. 投注有风险，请合理控制投注金额
3. 建议分散投注，降低单次损失风险
4. 定期重新分析数据，调整投注策略
5. 理性投注，切勿沉迷

## 技术支持

如有问题或建议，请检查：
1. 数据库连接配置是否正确
2. 依赖包是否完整安装
3. 数据表结构是否符合要求
4. Python版本是否兼容

## 模拟投注功能

### 📈 模拟投注脚本
系统还包含一个专门的模拟投注脚本 `模拟投注脚本.py`，用于对比两种投注策略的实际效果：

#### 策略对比
1. **策略1 (固定策略)**: 每期固定投注基础策略数字
2. **策略2 (排除策略)**: 排除上期各位置出现的数字后投注

#### 使用方法
```bash
# 运行模拟投注
python 模拟投注脚本.py

# 测试模拟投注功能
python 测试模拟投注.py
```

#### 输出结果
- **模拟投注报告.txt**: 详细的策略对比分析报告
- **模拟投注记录_策略1.json**: 策略1的详细投注记录
- **模拟投注记录_策略2.json**: 策略2的详细投注记录

#### 分析内容
- 两种策略的收益率对比
- 胜率统计和风险分析
- 每期详细的投注和开奖记录
- 累计收益趋势分析
- 最大连续亏损分析

## 项目文件说明

### 核心文件
- `积分游戏投注策略分析.py`: 主分析脚本，生成最佳投注策略
- `模拟投注脚本.py`: 模拟投注脚本，对比策略效果
- `配置.ini`: 数据库配置文件
- `requirements.txt`: Python依赖包列表

### 测试文件
- `测试脚本.py`: 主分析功能测试
- `测试模拟投注.py`: 模拟投注功能测试

### 文档文件
- `README.md`: 项目说明文档

## 完整使用流程

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 配置数据库连接
# 编辑 配置.ini 文件
```

### 2. 策略分析
```bash
# 运行策略分析，生成最佳投注策略
python 积分游戏投注策略分析.py
```

### 3. 模拟验证
```bash
# 运行模拟投注，验证策略效果
python 模拟投注脚本.py
```

### 4. 查看结果
- 查看 `积分游戏投注策略分析报告.txt` 了解最佳策略
- 查看 `模拟投注报告.txt` 了解策略对比结果
- 查看生成的图表文件

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础数据分析功能
- 实现多种投注策略生成
- 实现收益率计算和策略评估
- 实现可视化图表和报告生成
- 新增模拟投注功能
- 新增策略对比分析
- 新增详细的投注记录保存

---

**免责声明**: 本软件仅供学习和研究使用，不构成投资建议。使用者需自行承担投注风险。
