#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟投注脚本测试程序
用于测试模拟投注功能是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from 模拟投注脚本 import 模拟投注器
    print("✓ 成功导入模拟投注器")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)

def 测试基本功能():
    """测试模拟投注器的基本功能"""
    print("\n开始测试基本功能...")
    
    try:
        # 创建投注器实例
        投注器 = 模拟投注器()
        print("✓ 投注器实例创建成功")
        
        # 测试配置读取
        print(f"✓ 数据库配置: {投注器.配置}")
        
        # 测试基础策略
        print("✓ 基础策略配置:")
        for 位置, 数字列表 in 投注器.基础策略.items():
            print(f"  {位置}: {数字列表}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def 测试数据获取():
    """测试数据获取功能"""
    print("\n开始测试数据获取...")
    
    try:
        投注器 = 模拟投注器()
        
        # 测试数据库连接
        连接成功 = 投注器.获取历史数据()
        
        if 连接成功:
            print("✓ 数据库连接成功")
            print(f"✓ 获取到 {len(投注器.历史数据)} 条历史记录")
            
            # 显示数据样本
            if len(投注器.历史数据) > 0:
                样本数据 = 投注器.历史数据.iloc[0]
                开奖结果 = [样本数据[f'b{i}'] for i in range(1, 11)]
                print(f"✓ 数据样本: 期号 {样本数据['period']}, 开奖 {开奖结果}")
            
            return True
        else:
            print("✗ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据获取测试失败: {e}")
        return False

def 测试投注策略():
    """测试投注策略功能"""
    print("\n开始测试投注策略...")
    
    try:
        投注器 = 模拟投注器()
        
        # 模拟期数据
        模拟期数据 = type('obj', (object,), {})()  # 创建一个简单对象
        
        # 测试策略1
        策略1投注, 策略1投注额 = 投注器.策略1投注(模拟期数据)
        print(f"✓ 策略1投注测试成功，投注额: {策略1投注额} 积分")
        
        # 测试策略2 (无上期数据)
        策略2投注, 策略2投注额 = 投注器.策略2投注(模拟期数据, None)
        print(f"✓ 策略2投注测试成功 (无上期数据)，投注额: {策略2投注额} 积分")
        
        # 测试策略2 (有上期数据)
        上期开奖 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        策略2投注_排除, 策略2投注额_排除 = 投注器.策略2投注(模拟期数据, 上期开奖)
        print(f"✓ 策略2投注测试成功 (有上期数据)，投注额: {策略2投注额_排除} 积分")
        
        # 验证策略2确实排除了上期数字
        排除效果 = 策略2投注额 - 策略2投注额_排除
        print(f"✓ 策略2排除效果验证: 减少投注 {排除效果} 个数字")
        
        return True
        
    except Exception as e:
        print(f"✗ 投注策略测试失败: {e}")
        return False

def 测试收益计算():
    """测试收益计算功能"""
    print("\n开始测试收益计算...")
    
    try:
        投注器 = 模拟投注器()
        
        # 模拟投注信息
        投注信息 = {
            '位置1': [1, 2, 3],
            '位置2': [4, 5, 6],
            '位置3': [7, 8, 9],
            '位置4': [1, 2, 3],
            '位置5': [4, 5, 6],
            '位置6': [7, 8, 9],
            '位置7': [1, 2, 3],
            '位置8': [4, 5, 6],
            '位置9': [7, 8, 9],
            '位置10': [1, 2, 3]
        }
        
        # 模拟开奖结果 (部分中奖)
        开奖结果 = [1, 4, 7, 10, 5, 8, 2, 6, 9, 3]
        
        # 计算收益
        总收益, 中奖位置 = 投注器.计算收益(投注信息, 开奖结果)
        
        print(f"✓ 收益计算测试成功")
        print(f"  开奖结果: {开奖结果}")
        print(f"  总收益: {总收益:.1f} 积分")
        print(f"  中奖位置: {中奖位置}")
        
        # 验证收益计算逻辑
        预期中奖位置数 = len(中奖位置)
        预期收益 = 预期中奖位置数 * 9.9
        
        if abs(总收益 - 预期收益) < 0.1:
            print("✓ 收益计算逻辑验证通过")
        else:
            print(f"✗ 收益计算逻辑验证失败: 预期 {预期收益}, 实际 {总收益}")
        
        return True
        
    except Exception as e:
        print(f"✗ 收益计算测试失败: {e}")
        return False

def 测试小规模模拟():
    """测试小规模模拟投注"""
    print("\n开始测试小规模模拟投注...")
    
    try:
        投注器 = 模拟投注器()
        
        # 获取数据
        if not 投注器.获取历史数据():
            print("✗ 无法获取数据，跳过模拟测试")
            return False
        
        # 小规模模拟 (最近10期)
        总期数 = len(投注器.历史数据)
        测试期数 = min(10, 总期数)
        开始期数 = 总期数 - 测试期数
        
        print(f"  模拟期数: {测试期数} 期")
        
        # 执行模拟
        成功 = 投注器.模拟投注(开始期数=开始期数, 结束期数=总期数)
        
        if 成功:
            print("✓ 小规模模拟投注成功")
            
            # 验证记录数量
            if len(投注器.策略1记录) == 测试期数 and len(投注器.策略2记录) == 测试期数:
                print("✓ 投注记录数量正确")
            else:
                print(f"✗ 投注记录数量错误: 策略1 {len(投注器.策略1记录)}, 策略2 {len(投注器.策略2记录)}")
            
            # 显示简单统计
            print(f"  策略1净收益: {投注器.策略1统计['净收益']:.1f} 积分")
            print(f"  策略2净收益: {投注器.策略2统计['净收益']:.1f} 积分")
            print(f"  策略1胜率: {投注器.策略1统计['胜率']:.1f}%")
            print(f"  策略2胜率: {投注器.策略2统计['胜率']:.1f}%")
            
            return True
        else:
            print("✗ 小规模模拟投注失败")
            return False
        
    except Exception as e:
        print(f"✗ 小规模模拟测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("模拟投注脚本测试程序")
    print("=" * 40)
    
    测试结果 = []
    
    # 执行各项测试
    测试结果.append(("基本功能", 测试基本功能()))
    测试结果.append(("数据获取", 测试数据获取()))
    测试结果.append(("投注策略", 测试投注策略()))
    测试结果.append(("收益计算", 测试收益计算()))
    测试结果.append(("小规模模拟", 测试小规模模拟()))
    
    # 显示测试结果
    print("\n" + "=" * 40)
    print("测试结果汇总:")
    print("-" * 20)
    
    成功数 = 0
    for 测试名称, 结果 in 测试结果:
        状态 = "✓ 通过" if 结果 else "✗ 失败"
        print(f"{测试名称}: {状态}")
        if 结果:
            成功数 += 1
    
    print(f"\n总体结果: {成功数}/{len(测试结果)} 项测试通过")
    
    if 成功数 == len(测试结果):
        print("🎉 所有测试通过！模拟投注脚本可以正常使用。")
        print("\n建议运行完整模拟投注:")
        print("python 模拟投注脚本.py")
    else:
        print("⚠️  部分测试失败，请检查配置和数据库连接。")

if __name__ == "__main__":
    main()
