#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分游戏模拟投注脚本
基于最佳策略进行模拟投注，对比两种策略的效果
"""

import mysql.connector
import pandas as pd
import numpy as np
import configparser
from datetime import datetime
from collections import defaultdict
import json

class 模拟投注器:
    """模拟投注器类"""

    def __init__(self, 配置文件路径='配置.ini'):
        """初始化投注器"""
        self.配置 = self._读取配置(配置文件路径)
        self.历史数据 = None

        # 基础策略 (从报告中提取)
        self.基础策略 = {
            '位置1': [2, 8, 4],
            '位置2': [9, 6, 5],
            '位置3': [1, 7, 8],
            '位置4': [10, 1, 3],
            '位置5': [7, 5, 6],
            '位置6': [9, 2, 1],
            '位置7': [8, 3, 10],
            '位置8': [10, 6, 9],
            '位置9': [7, 4, 1],
            '位置10': [10, 3, 5]
        }

        # 投注记录
        self.策略1记录 = []
        self.策略2记录 = []

    def _读取配置(self, 配置文件路径):
        """读取数据库配置"""
        config = configparser.ConfigParser()
        config.read(配置文件路径, encoding='utf-8')
        return {
            'host': config.get('database', 'host'),
            'user': config.get('database', 'user'),
            'password': config.get('database', 'password'),
            'database': config.get('database', 'database'),
            'port': config.getint('database', 'port')
        }

    def 获取历史数据(self):
        """从数据库获取历史开奖数据"""
        try:
            连接 = mysql.connector.connect(**self.配置)
            查询语句 = """
            SELECT period, drawTime, b1, b2, b3, b4, b5, b6, b7, b8, b9, b10
            FROM sgwin_history
            WHERE gameType = 0
            ORDER BY drawTime ASC
            """
            self.历史数据 = pd.read_sql(查询语句, 连接)
            连接.close()

            print(f"成功获取 {len(self.历史数据)} 条历史开奖记录")
            return True

        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False

    def 策略1投注(self, 期数据):
        """策略1: 固定投注基础策略数字"""
        投注信息 = {}
        总投注 = 0

        for 位置, 数字列表 in self.基础策略.items():
            投注信息[位置] = 数字列表.copy()
            总投注 += len(数字列表)

        return 投注信息, 总投注

    def 策略2投注(self, 期数据, 上期开奖):
        """策略2: 排除上期出现的数字"""
        投注信息 = {}
        总投注 = 0

        for i, (位置, 数字列表) in enumerate(self.基础策略.items()):
            if 上期开奖 is not None:
                # 获取上期该位置的开奖数字
                上期数字 = 上期开奖[i]
                # 排除上期出现的数字
                过滤后数字 = [数字 for 数字 in 数字列表 if 数字 != 上期数字]
            else:
                过滤后数字 = 数字列表.copy()

            投注信息[位置] = 过滤后数字
            总投注 += len(过滤后数字)

        return 投注信息, 总投注

    def 计算收益(self, 投注信息, 开奖结果):
        """计算单期收益"""
        总收益 = 0
        中奖位置 = []

        for i, (位置, 投注数字) in enumerate(投注信息.items()):
            开奖数字 = 开奖结果[i]
            if 开奖数字 in 投注数字:
                总收益 += 9.9  # 中奖获得9.9倍积分
                中奖位置.append(位置)

        return 总收益, 中奖位置

    def 模拟投注(self, 开始期数=0, 结束期数=None):
        """执行模拟投注"""
        if self.历史数据 is None:
            print("请先获取历史数据")
            return False

        if 结束期数 is None:
            结束期数 = len(self.历史数据)

        print(f"开始模拟投注，期数范围: {开始期数} - {结束期数}")
        print("=" * 60)

        # 初始化统计变量
        策略1统计 = {'总投注': 0, '总收益': 0, '中奖次数': 0, '总期数': 0}
        策略2统计 = {'总投注': 0, '总收益': 0, '中奖次数': 0, '总期数': 0}

        上期开奖 = None

        for i in range(开始期数, min(结束期数, len(self.历史数据))):
            期数据 = self.历史数据.iloc[i]
            期号 = 期数据['period']
            开奖时间 = 期数据['drawTime']
            开奖结果 = [期数据[f'b{j}'] for j in range(1, 11)]

            # 策略1投注
            策略1投注信息, 策略1投注额 = self.策略1投注(期数据)
            策略1收益, 策略1中奖位置 = self.计算收益(策略1投注信息, 开奖结果)
            策略1净收益 = 策略1收益 - 策略1投注额

            # 策略2投注
            策略2投注信息, 策略2投注额 = self.策略2投注(期数据, 上期开奖)
            策略2收益, 策略2中奖位置 = self.计算收益(策略2投注信息, 开奖结果)
            策略2净收益 = 策略2收益 - 策略2投注额

            # 记录详细信息
            策略1记录 = {
                '期号': 期号,
                '开奖时间': 开奖时间,
                '开奖结果': 开奖结果,
                '投注信息': 策略1投注信息,
                '投注额': 策略1投注额,
                '收益': 策略1收益,
                '净收益': 策略1净收益,
                '中奖位置': 策略1中奖位置,
                '是否中奖': len(策略1中奖位置) > 0
            }

            策略2记录 = {
                '期号': 期号,
                '开奖时间': 开奖时间,
                '开奖结果': 开奖结果,
                '投注信息': 策略2投注信息,
                '投注额': 策略2投注额,
                '收益': 策略2收益,
                '净收益': 策略2净收益,
                '中奖位置': 策略2中奖位置,
                '是否中奖': len(策略2中奖位置) > 0
            }

            self.策略1记录.append(策略1记录)
            self.策略2记录.append(策略2记录)

            # 更新统计
            策略1统计['总投注'] += 策略1投注额
            策略1统计['总收益'] += 策略1收益
            策略1统计['总期数'] += 1
            if 策略1记录['是否中奖']:
                策略1统计['中奖次数'] += 1

            策略2统计['总投注'] += 策略2投注额
            策略2统计['总收益'] += 策略2收益
            策略2统计['总期数'] += 1
            if 策略2记录['是否中奖']:
                策略2统计['中奖次数'] += 1

            # 显示前10期的详细信息
            if i < 开始期数 + 10:
                print(f"第 {期号} 期:")
                print(f"  开奖结果: {开奖结果}")
                print(f"  策略1 - 投注: {策略1投注额}积分, 收益: {策略1收益:.1f}积分, 净收益: {策略1净收益:.1f}积分")
                print(f"  策略2 - 投注: {策略2投注额}积分, 收益: {策略2收益:.1f}积分, 净收益: {策略2净收益:.1f}积分")
                if 策略1中奖位置:
                    print(f"  策略1中奖位置: {策略1中奖位置}")
                if 策略2中奖位置:
                    print(f"  策略2中奖位置: {策略2中奖位置}")
                print()

            # 更新上期开奖结果
            上期开奖 = 开奖结果

        # 计算最终统计结果
        self.策略1统计 = self._计算最终统计(策略1统计)
        self.策略2统计 = self._计算最终统计(策略2统计)

        return True

    def _计算最终统计(self, 统计数据):
        """计算最终统计结果"""
        净收益 = 统计数据['总收益'] - 统计数据['总投注']
        收益率 = (净收益 / 统计数据['总投注'] * 100) if 统计数据['总投注'] > 0 else 0
        胜率 = (统计数据['中奖次数'] / 统计数据['总期数'] * 100) if 统计数据['总期数'] > 0 else 0

        统计数据.update({
            '净收益': 净收益,
            '收益率': 收益率,
            '胜率': 胜率
        })

        return 统计数据

    def 生成详细报告(self):
        """生成详细的投注报告"""
        报告内容 = []
        报告内容.append("=" * 80)
        报告内容.append("积分游戏模拟投注报告")
        报告内容.append("=" * 80)
        报告内容.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        报告内容.append(f"模拟期数: {len(self.策略1记录)} 期")
        报告内容.append("")

        # 基础策略说明
        报告内容.append("一、投注策略说明")
        报告内容.append("-" * 40)
        报告内容.append("策略1 (固定策略): 每期固定投注基础策略数字")
        报告内容.append("策略2 (排除策略): 排除上期各位置出现的数字后投注")
        报告内容.append("")
        报告内容.append("基础投注策略:")
        for 位置, 数字列表 in self.基础策略.items():
            报告内容.append(f"  {位置}: {数字列表}")
        报告内容.append("")

        # 整体统计对比
        报告内容.append("二、整体统计对比")
        报告内容.append("-" * 40)
        报告内容.append(f"{'指标':<15} {'策略1':<15} {'策略2':<15} {'差异':<15}")
        报告内容.append("-" * 60)

        指标列表 = [
            ('总投注', '总投注', '积分'),
            ('总收益', '总收益', '积分'),
            ('净收益', '净收益', '积分'),
            ('收益率', '收益率', '%'),
            ('胜率', '胜率', '%'),
            ('中奖次数', '中奖次数', '次')
        ]

        for 指标名, 键名, 单位 in 指标列表:
            策略1值 = self.策略1统计[键名]
            策略2值 = self.策略2统计[键名]
            差异 = 策略2值 - 策略1值

            if 单位 == '%':
                报告内容.append(f"{指标名:<15} {策略1值:<14.2f}% {策略2值:<14.2f}% {差异:<+14.2f}%")
            else:
                报告内容.append(f"{指标名:<15} {策略1值:<14.1f}{单位} {策略2值:<14.1f}{单位} {差异:<+14.1f}{单位}")

        报告内容.append("")

        # 详细期数分析
        报告内容.append("三、详细期数分析 (前20期)")
        报告内容.append("-" * 40)
        报告内容.append(f"{'期号':<10} {'开奖结果':<30} {'策略1净收益':<12} {'策略2净收益':<12}")
        报告内容.append("-" * 70)

        for i, (记录1, 记录2) in enumerate(zip(self.策略1记录[:20], self.策略2记录[:20])):
            期号 = 记录1['期号']
            开奖结果 = str(记录1['开奖结果'])
            策略1净收益 = 记录1['净收益']
            策略2净收益 = 记录2['净收益']

            报告内容.append(f"{期号:<10} {开奖结果:<30} {策略1净收益:<+11.1f} {策略2净收益:<+11.1f}")

        报告内容.append("")

        # 收益趋势分析
        报告内容.append("四、收益趋势分析")
        报告内容.append("-" * 40)

        # 计算累计收益
        策略1累计 = 0
        策略2累计 = 0
        累计收益点 = []

        for i, (记录1, 记录2) in enumerate(zip(self.策略1记录, self.策略2记录)):
            策略1累计 += 记录1['净收益']
            策略2累计 += 记录2['净收益']

            if (i + 1) % 50 == 0 or i == len(self.策略1记录) - 1:
                累计收益点.append((i + 1, 策略1累计, 策略2累计))

        报告内容.append(f"{'期数':<10} {'策略1累计':<15} {'策略2累计':<15} {'差异':<15}")
        报告内容.append("-" * 55)

        for 期数, 策略1累计, 策略2累计 in 累计收益点:
            差异 = 策略2累计 - 策略1累计
            报告内容.append(f"{期数:<10} {策略1累计:<+14.1f} {策略2累计:<+14.1f} {差异:<+14.1f}")

        报告内容.append("")

        # 策略优劣分析
        报告内容.append("五、策略优劣分析")
        报告内容.append("-" * 40)

        if self.策略2统计['收益率'] > self.策略1统计['收益率']:
            优势策略 = "策略2 (排除策略)"
            优势幅度 = self.策略2统计['收益率'] - self.策略1统计['收益率']
        else:
            优势策略 = "策略1 (固定策略)"
            优势幅度 = self.策略1统计['收益率'] - self.策略2统计['收益率']

        报告内容.append(f"收益率最优策略: {优势策略}")
        报告内容.append(f"收益率优势: {优势幅度:.2f}%")
        报告内容.append("")

        if self.策略2统计['胜率'] > self.策略1统计['胜率']:
            胜率优势策略 = "策略2 (排除策略)"
            胜率优势幅度 = self.策略2统计['胜率'] - self.策略1统计['胜率']
        else:
            胜率优势策略 = "策略1 (固定策略)"
            胜率优势幅度 = self.策略1统计['胜率'] - self.策略2统计['胜率']

        报告内容.append(f"胜率最优策略: {胜率优势策略}")
        报告内容.append(f"胜率优势: {胜率优势幅度:.2f}%")
        报告内容.append("")

        # 风险分析
        报告内容.append("六、风险分析")
        报告内容.append("-" * 40)

        # 计算最大连续亏损
        策略1最大亏损, 策略1最大连亏 = self._计算最大连续亏损(self.策略1记录)
        策略2最大亏损, 策略2最大连亏 = self._计算最大连续亏损(self.策略2记录)

        报告内容.append(f"策略1最大单期亏损: {策略1最大亏损:.1f}积分")
        报告内容.append(f"策略1最大连续亏损期数: {策略1最大连亏}期")
        报告内容.append(f"策略2最大单期亏损: {策略2最大亏损:.1f}积分")
        报告内容.append(f"策略2最大连续亏损期数: {策略2最大连亏}期")
        报告内容.append("")

        # 结论和建议
        报告内容.append("七、结论和建议")
        报告内容.append("-" * 40)
        报告内容.append("基于本次模拟投注结果:")
        报告内容.append(f"1. {优势策略}在收益率方面表现更优")
        报告内容.append(f"2. {胜率优势策略}在胜率方面表现更优")

        if 优势策略 == 胜率优势策略:
            报告内容.append(f"3. 推荐使用{优势策略}")
        else:
            报告内容.append("3. 两种策略各有优势，建议根据风险偏好选择")

        报告内容.append("4. 建议定期重新评估策略效果")
        报告内容.append("5. 注意控制投注金额，理性投注")

        报告内容.append("")
        报告内容.append("=" * 80)
        报告内容.append("报告结束")
        报告内容.append("=" * 80)

        return "\n".join(报告内容)

    def _计算最大连续亏损(self, 记录列表):
        """计算最大连续亏损"""
        最大单期亏损 = 0
        最大连续亏损期数 = 0
        当前连续亏损 = 0

        for 记录 in 记录列表:
            净收益 = 记录['净收益']

            # 更新最大单期亏损
            if 净收益 < 最大单期亏损:
                最大单期亏损 = 净收益

            # 计算连续亏损
            if 净收益 < 0:
                当前连续亏损 += 1
                最大连续亏损期数 = max(最大连续亏损期数, 当前连续亏损)
            else:
                当前连续亏损 = 0

        return 最大单期亏损, 最大连续亏损期数

    def 保存投注记录(self, 文件名前缀="模拟投注记录"):
        """保存详细的投注记录到文件"""
        import json

        # 保存策略1记录
        策略1文件名 = f"{文件名前缀}_策略1.json"
        with open(策略1文件名, 'w', encoding='utf-8') as f:
            json.dump(self.策略1记录, f, ensure_ascii=False, indent=2, default=str)

        # 保存策略2记录
        策略2文件名 = f"{文件名前缀}_策略2.json"
        with open(策略2文件名, 'w', encoding='utf-8') as f:
            json.dump(self.策略2记录, f, ensure_ascii=False, indent=2, default=str)

        print(f"投注记录已保存:")
        print(f"  策略1: {策略1文件名}")
        print(f"  策略2: {策略2文件名}")

    def 保存报告(self, 报告内容, 文件名="模拟投注报告.txt"):
        """保存报告到文件"""
        with open(文件名, 'w', encoding='utf-8') as f:
            f.write(报告内容)
        print(f"模拟投注报告已保存为: {文件名}")

    def 显示统计摘要(self):
        """显示统计摘要"""
        print("\n" + "=" * 60)
        print("模拟投注结果摘要")
        print("=" * 60)

        print(f"模拟期数: {len(self.策略1记录)} 期")
        print()

        print("策略对比:")
        print("-" * 40)
        print(f"{'指标':<12} {'策略1':<12} {'策略2':<12} {'差异':<12}")
        print("-" * 48)

        指标数据 = [
            ('总投注', self.策略1统计['总投注'], self.策略2统计['总投注']),
            ('总收益', self.策略1统计['总收益'], self.策略2统计['总收益']),
            ('净收益', self.策略1统计['净收益'], self.策略2统计['净收益']),
            ('收益率%', self.策略1统计['收益率'], self.策略2统计['收益率']),
            ('胜率%', self.策略1统计['胜率'], self.策略2统计['胜率']),
            ('中奖次数', self.策略1统计['中奖次数'], self.策略2统计['中奖次数'])
        ]

        for 指标, 策略1值, 策略2值 in 指标数据:
            差异 = 策略2值 - 策略1值
            print(f"{指标:<12} {策略1值:<11.1f} {策略2值:<11.1f} {差异:<+11.1f}")

        print()

        # 显示最优策略
        if self.策略2统计['收益率'] > self.策略1统计['收益率']:
            最优策略 = "策略2 (排除策略)"
            优势 = self.策略2统计['收益率'] - self.策略1统计['收益率']
        else:
            最优策略 = "策略1 (固定策略)"
            优势 = self.策略1统计['收益率'] - self.策略2统计['收益率']

        print(f"收益率最优策略: {最优策略}")
        print(f"收益率优势: {优势:.2f}%")
        print()


def main():
    """主函数"""
    print("积分游戏模拟投注系统")
    print("=" * 50)

    # 创建模拟投注器
    投注器 = 模拟投注器()

    # 获取历史数据
    print("正在获取历史数据...")
    if not 投注器.获取历史数据():
        print("数据获取失败，程序退出")
        return

    # 设置模拟参数
    总期数 = len(投注器.历史数据)
    模拟期数 = min(200, 总期数)  # 模拟最近200期，如果数据不足则全部模拟

    print(f"历史数据总期数: {总期数}")
    print(f"本次模拟期数: {模拟期数}")
    print()

    # 执行模拟投注
    print("开始模拟投注...")
    开始期数 = 总期数 - 模拟期数
    成功 = 投注器.模拟投注(开始期数=开始期数, 结束期数=总期数)

    if not 成功:
        print("模拟投注失败")
        return

    # 显示结果摘要
    投注器.显示统计摘要()

    # 生成详细报告
    print("正在生成详细报告...")
    报告内容 = 投注器.生成详细报告()
    投注器.保存报告(报告内容)

    # 保存投注记录
    print("正在保存投注记录...")
    投注器.保存投注记录()

    print("\n" + "=" * 50)
    print("模拟投注完成！")
    print("=" * 50)
    print("生成的文件:")
    print("1. 模拟投注报告.txt - 详细分析报告")
    print("2. 模拟投注记录_策略1.json - 策略1详细记录")
    print("3. 模拟投注记录_策略2.json - 策略2详细记录")
    print()
    print("建议查看详细报告了解更多分析结果")


if __name__ == "__main__":
    main()
